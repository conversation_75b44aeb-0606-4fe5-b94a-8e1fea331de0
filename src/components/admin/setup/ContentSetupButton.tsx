import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useContentSetup } from "@/utils/setupContent";
import { CheckCircle, AlertCircle, Loader2, FileText } from "lucide-react";
import { toast } from "sonner";

export const ContentSetupButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  const { setupAllContent } = useContentSetup();

  const handleSetupContent = async () => {
    setIsLoading(true);
    setError(null);
    setResults([]);

    try {
      const setupResults = await setupAllContent();
      setResults(setupResults);
      toast.success("Content setup completed successfully!");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      toast.error(`Content setup failed: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Automated Content Setup
        </CardTitle>
        <p className="text-sm text-gray-600">
          This will automatically create essential content entries from content.md including:
          Cybersecurity, Electronic Security, and Network Solutions services in both Spanish and English.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={handleSetupContent}
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Setting up content...
            </>
          ) : (
            <>
              <FileText className="w-4 h-4 mr-2" />
              Setup Content from content.md
            </>
          )}
        </Button>

        {/* Results Display */}
        {results.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-green-700 flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              Setup Results:
            </h4>
            {results.map((result, index) => (
              <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                {result}
              </Badge>
            ))}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-700 font-medium mb-2">
              <AlertCircle className="w-4 h-4" />
              Setup Failed
            </div>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>What this creates:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Cybersecurity service (Spanish & English)</li>
            <li>Electronic Security service (Spanish & English)</li>
            <li>Network Solutions service (Spanish & English)</li>
          </ul>
          <p className="mt-2">
            <strong>Note:</strong> This will create or update existing content with the same identifiers.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
