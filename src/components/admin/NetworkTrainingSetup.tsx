import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useNetworkAndTrainingSetup } from '@/utils/setupNetworkAndTraining';
import { Network, GraduationCap, Loader2, CheckCircle, AlertCircle, Play } from 'lucide-react';
import { toast } from 'sonner';

export const NetworkTrainingSetup: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [setupResults, setSetupResults] = useState<{
    networkSolutions?: number;
    trainingPrograms?: number;
    total?: number;
  } | null>(null);

  const {
    setupNetworkSolutions,
    setupTrainingPrograms,
    setupAllContent,
    getExistingCounts,
    isReady,
    isContentLoading
  } = useNetworkAndTrainingSetup();

  const existingCounts = getExistingCounts();
  const hasNetworkSolutions = existingCounts.networkSolutions > 0;
  const hasTrainingPrograms = existingCounts.trainingPrograms > 0;
  const isSetupComplete = hasNetworkSolutions && hasTrainingPrograms;

  // Debug logging
  console.log("NetworkTrainingSetup Debug:", {
    isReady,
    isContentLoading,
    existingCounts,
    hasNetworkSolutions,
    hasTrainingPrograms,
    isSetupComplete
  });

  const handleSetupNetworkSolutions = async () => {
    if (!isReady) {
      toast.error(`Content types not ready. isReady: ${isReady}, isContentLoading: ${isContentLoading}`);
      return;
    }

    setIsLoading(true);
    try {
      const count = await setupNetworkSolutions();
      toast.success(`Successfully created ${count} network solutions!`);
      setSetupResults(prev => ({ ...prev, networkSolutions: count }));
      // Force a small delay to allow the database to update and queries to refresh
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Network solutions setup error:", error);
      toast.error("Failed to setup network solutions: " + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetupTrainingPrograms = async () => {
    if (!isReady) {
      toast.error("Content types not ready. Please wait...");
      return;
    }

    setIsLoading(true);
    try {
      const count = await setupTrainingPrograms();
      toast.success(`Successfully created ${count} training programs!`);
      setSetupResults(prev => ({ ...prev, trainingPrograms: count }));
      // Force a small delay to allow the database to update and queries to refresh
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Training programs setup error:", error);
      toast.error("Failed to setup training programs: " + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetupAll = async () => {
    if (!isReady) {
      toast.error("Content types not ready. Please wait...");
      return;
    }

    setIsLoading(true);
    try {
      const results = await setupAllContent();
      setSetupResults(results);
      // Force a small delay to allow the database to update and queries to refresh
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Full setup error:", error);
      toast.error("Failed to setup content: " + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Network Solutions & Training Setup</h2>
          <p className="text-gray-600 mt-1">
            Add network solutions and training programs to your content database
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {!isReady ? (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Loader2 className="w-3 h-3 animate-spin" />
              <span>{isContentLoading ? "Loading content..." : "Loading types..."}</span>
            </Badge>
          ) : (
            <Badge variant="default" className="flex items-center space-x-1">
              <CheckCircle className="w-3 h-3" />
              <span>Ready</span>
            </Badge>
          )}
        </div>
      </div>

      {/* Setup Results or Existing Content Status */}
      {(setupResults || isSetupComplete) && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800 flex items-center space-x-2">
              <CheckCircle className="w-5 h-5" />
              <span>{setupResults ? "Setup Complete!" : "Content Already Exists"}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700">
                  {setupResults?.networkSolutions ?? existingCounts.networkSolutions}
                </div>
                <div className="text-sm text-green-600">Network Solutions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700">
                  {setupResults?.trainingPrograms ?? existingCounts.trainingPrograms}
                </div>
                <div className="text-sm text-green-600">Training Programs</div>
              </div>
              {setupResults?.total && (
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-700">{setupResults.total}</div>
                  <div className="text-sm text-green-600">Total Items Created</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Network Solutions Setup */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Network className="w-5 h-5 text-blue-600" />
              <span>Network Solutions</span>
            </CardTitle>
            <CardDescription>
              Add comprehensive network connectivity solutions to your database
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Includes:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• National Connectivity (Bata, Malabo & Mongomo)</li>
                <li>• International Connectivity (MPLS, SD-WAN, VPL)</li>
                <li>• Branch Office Connectivity</li>
                <li>• Enterprise Internet Services</li>
                <li>• Domestic Internet Services</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Features:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Bilingual content (English & Spanish)</li>
                <li>• Detailed service descriptions</li>
                <li>• Technical specifications</li>
                <li>• Target market information</li>
                <li>• Pricing and contact details</li>
              </ul>
            </div>

            {hasNetworkSolutions ? (
              <div className="flex items-center justify-center p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                <span className="text-green-700 font-medium">Already Set Up</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Button
                  onClick={handleSetupNetworkSolutions}
                  disabled={!isReady || isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Setting up...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Setup Network Solutions
                    </>
                  )}
                </Button>
                {!isReady && (
                  <Button
                    onClick={handleSetupNetworkSolutions}
                    variant="outline"
                    className="w-full text-xs"
                    disabled={isLoading}
                  >
                    Force Setup (Debug)
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Training Programs Setup */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <GraduationCap className="w-5 h-5 text-purple-600" />
              <span>Training Programs</span>
            </CardTitle>
            <CardDescription>
              Add comprehensive training programs to your database
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Includes:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Corporate PC Skills Development</li>
                <li>• Leadership Training Program</li>
                <li>• Time Management Training</li>
                <li>• Communication Skills Training</li>
                <li>• Oil & Gas Professional Training</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Features:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Bilingual content (English & Spanish)</li>
                <li>• Detailed curriculum modules</li>
                <li>• Learning objectives and benefits</li>
                <li>• Target audience information</li>
                <li>• Delivery options and pricing</li>
              </ul>
            </div>

            {hasTrainingPrograms ? (
              <div className="flex items-center justify-center p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                <span className="text-green-700 font-medium">Already Set Up</span>
              </div>
            ) : (
              <div className="space-y-2">
                <Button
                  onClick={handleSetupTrainingPrograms}
                  disabled={!isReady || isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Setting up...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Setup Training Programs
                    </>
                  )}
                </Button>
                {!isReady && (
                  <Button
                    onClick={handleSetupTrainingPrograms}
                    variant="outline"
                    className="w-full text-xs"
                    disabled={isLoading}
                  >
                    Force Setup (Debug)
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Setup All Button - Only show if not everything is set up */}
      {!isSetupComplete && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-blue-900">Setup Everything</h3>
                <p className="text-blue-700">
                  Create all network solutions and training programs in one go
                </p>
              </div>

              <Button
                onClick={handleSetupAll}
                disabled={!isReady || isLoading}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Setting up all content...
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5 mr-2" />
                    Setup All Content
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Warning Notice */}
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5" />
            <div className="space-y-1">
              <h4 className="font-medium text-amber-900">Important Notes</h4>
              <ul className="text-sm text-amber-800 space-y-1">
                <li>• This will create new content entries in your database</li>
                <li>• If content with the same identifier exists, it will be updated</li>
                <li>• All content will be created in both English and Spanish</li>
                <li>• You can edit all content through the Content Management interface after setup</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
