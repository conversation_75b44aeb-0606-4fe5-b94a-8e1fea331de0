// Manual content setup utility
// This creates the essential content entries from content.md

export const createCybersecurityContent = async (convex: any) => {
  try {
    // First ensure the service_card content type exists
    const contentType = await convex.query("content:getContentTypeByName", { name: "service_card" });
    if (!contentType) {
      console.error("service_card content type not found");
      return;
    }

    // Create Spanish Cybersecurity content
    const spanishData = {
      title: "Ciberseguridad",
      slug: "ciberseguridad", 
      description: "Protegiendo tu Mundo Digital en Cada Fase. En la era digital, la ciberseguridad se ha convertido en un pilar fundamental para proteger la información, los sistemas y las redes contra amenazas cibernéticas.",
      fullDescription: `<h2>¿Qué es la Ciberseguridad?</h2>
<p>La ciberseguridad es el conjunto de prácticas, tecnologías y procesos diseñados para proteger sistemas, redes, dispositivos y datos de accesos no autorizados, ataques maliciosos o daños. Su objetivo es garantizar la confidencialidad, integridad y disponibilidad de la información en un entorno digital cada vez más interconectado.</p>

<h3>Las Fases de la Ciberseguridad</h3>
<h4>1. Prevención</h4>
<p>La prevención es la primera línea de defensa. En esta fase, se implementan medidas proactivas para evitar posibles ataques:</p>
<ul>
<li>Firewalls y antivirus: Herramientas esenciales para bloquear accesos no autorizados y detectar software malicioso.</li>
<li>Actualizaciones de software: Mantener sistemas y aplicaciones al día para corregir vulnerabilidades conocidas.</li>
<li>Educación y concienciación: Capacitar a los usuarios para identificar amenazas como phishing o ingeniería social.</li>
</ul>

<h4>2. Detección</h4>
<p>A pesar de las medidas preventivas, ningún sistema es 100% inmune. La detección temprana de amenazas es crucial:</p>
<ul>
<li>Monitoreo continuo: Uso de herramientas de inteligencia artificial y machine learning para identificar comportamientos sospechosos.</li>
<li>Sistemas de detección de intrusos (IDS): Alertan sobre actividades anómalas en la red.</li>
<li>Análisis de logs: Revisión de registros para identificar patrones de ataque.</li>
</ul>

<h4>3. Respuesta</h4>
<p>Cuando se detecta una amenaza, es fundamental actuar rápidamente:</p>
<ul>
<li>Contención: Aislar sistemas afectados para evitar la propagación del ataque.</li>
<li>Eradicación: Eliminar la causa del problema, como malware o vulnerabilidades explotadas.</li>
<li>Comunicación: Informar a las partes interesadas y, en algunos casos, a las autoridades competentes.</li>
</ul>

<h4>4. Recuperación</h4>
<p>Después de un incidente, es esencial restaurar la normalidad y aprender de lo ocurrido:</p>
<ul>
<li>Restauración de datos: Recuperar información a partir de copias de seguridad.</li>
<li>Evaluación post-incidente: Analizar qué sucedió y cómo se puede mejorar la seguridad.</li>
<li>Refuerzo de medidas: Implementar mejoras para evitar futuros ataques similares.</li>
</ul>

<h4>5. Mejora Continua</h4>
<p>La ciberseguridad no es un proceso estático, sino un ciclo constante de mejora:</p>
<ul>
<li>Auditorías regulares: Evaluar la efectividad de las medidas de seguridad.</li>
<li>Adaptación a nuevas amenazas: Mantenerse al tanto de las últimas tendencias en ciberataques.</li>
<li>Innovación tecnológica: Adoptar nuevas herramientas y metodologías para fortalecer la protección.</li>
</ul>`,
      icon: "Shield",
      features: ["Prevención de amenazas", "Detección temprana", "Respuesta rápida", "Recuperación completa", "Mejora continua"],
      ctaText: "Protege tu negocio",
      ctaUrl: "/contact"
    };

    await convex.mutation("content:createOrUpdateContent", {
      identifier: "service-cybersecurity",
      language: "es",
      contentTypeId: contentType._id,
      data: spanishData,
      status: "published"
    });

    // Create English Cybersecurity content
    const englishData = {
      title: "Cybersecurity",
      slug: "cybersecurity",
      description: "Protecting Your Digital World at Every Stage. In the digital age, cybersecurity has become a fundamental pillar for protecting information, systems, and networks against cyber threats.",
      fullDescription: `<h2>What is Cybersecurity?</h2>
<p>Cybersecurity is the set of practices, technologies, and processes designed to protect systems, networks, devices, and data from unauthorized access, malicious attacks, or damage. Its goal is to ensure the confidentiality, integrity, and availability of information in an increasingly interconnected digital environment.</p>

<h3>The Phases of Cybersecurity</h3>
<h4>1. Prevention</h4>
<p>Prevention is the first line of defense. In this phase, proactive measures are implemented to prevent potential attacks:</p>
<ul>
<li>Firewalls and antivirus software: Essential tools to block unauthorized access and detect malicious software.</li>
<li>Software updates: Keeping systems and applications up to date to fix known vulnerabilities.</li>
<li>Education and awareness: Training users to identify threats like phishing or social engineering.</li>
</ul>

<h4>2. Detection</h4>
<p>Despite preventive measures, no system is 100% immune. Early threat detection is crucial:</p>
<ul>
<li>Continuous monitoring: Using artificial intelligence and machine learning tools to identify suspicious behavior.</li>
<li>Intrusion detection systems (IDS): Alert about anomalous activities on the network.</li>
<li>Log analysis: Reviewing records to identify attack patterns.</li>
</ul>

<h4>3. Response</h4>
<p>When a threat is detected, it is essential to act quickly:</p>
<ul>
<li>Containment: Isolating affected systems to prevent attack propagation.</li>
<li>Eradication: Eliminating the cause of the problem, such as malware or exploited vulnerabilities.</li>
<li>Communication: Informing stakeholders and, in some cases, competent authorities.</li>
</ul>

<h4>4. Recovery</h4>
<p>After an incident, it is essential to restore normalcy and learn from what happened:</p>
<ul>
<li>Data restoration: Recovering information from backups.</li>
<li>Post-incident evaluation: Analyzing what happened and how security can be improved.</li>
<li>Reinforcement of measures: Implementing improvements to prevent future similar attacks.</li>
</ul>

<h4>5. Continuous Improvement</h4>
<p>Cybersecurity is not a static process but a constant cycle of improvement:</p>
<ul>
<li>Regular audits: Assessing the effectiveness of security measures.</li>
<li>Adaptation to new threats: Staying informed about the latest trends in cyberattacks.</li>
<li>Technological innovation: Adopting new tools and methodologies to strengthen protection.</li>
</ul>`,
      icon: "Shield", 
      features: ["Threat Prevention", "Early Detection", "Rapid Response", "Complete Recovery", "Continuous Improvement"],
      ctaText: "Protect Your Business",
      ctaUrl: "/contact"
    };

    await convex.mutation("content:createOrUpdateContent", {
      identifier: "service-cybersecurity", 
      language: "en",
      contentTypeId: contentType._id,
      data: englishData,
      status: "published"
    });

    console.log("Cybersecurity content created successfully");
  } catch (error) {
    console.error("Error creating cybersecurity content:", error);
  }
};

// Usage: Call this function from the browser console with the convex client
// createCybersecurityContent(convex);
